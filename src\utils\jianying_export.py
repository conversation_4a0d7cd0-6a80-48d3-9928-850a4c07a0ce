# -*- coding: utf-8 -*-
"""
剪映导出工具 - 将素材导出到剪映草稿目录
"""

import os
import json
import shutil
import platform
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from utils.config_manager import ConfigManager


class JianyingExporter:
    """剪映导出工具"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.jianying_path = self._detect_jianying_path()
        self.draft_path = self._get_draft_path()
    
    def _detect_jianying_path(self) -> str:
        """自动检测剪映安装路径"""
        system = platform.system()
        
        if system == "Windows":
            # Windows下的可能路径
            possible_paths = [
                Path.home() / "AppData/Local/JianyingPro",
                Path("C:/Program Files/JianyingPro"),
                Path("C:/Program Files (x86)/JianyingPro"),
            ]
        elif system == "Darwin":  # macOS
            possible_paths = [
                Path.home() / "Movies/JianyingPro",
                Path("/Applications/JianyingPro.app/Contents/Resources",)
            ]
        else:
            return ""
        
        for path in possible_paths:
            if path.exists():
                return str(path)
        
        return ""
    
    def _get_draft_path(self) -> str:
        """获取剪映草稿路径"""
        if not self.jianying_path:
            return ""
        
        system = platform.system()
        
        if system == "Windows":
            draft_path = Path(self.jianying_path) / "User Data/Projects/com.lveditor.draft"
        elif system == "Darwin":  # macOS
            draft_path = Path.home() / "Movies/JianyingPro/User Data/Projects/com.lveditor.draft"
        else:
            return ""
        
        return str(draft_path) if draft_path.exists() else ""
    
    def is_jianying_available(self) -> bool:
        """检查剪映是否可用"""
        return bool(self.jianying_path and self.draft_path)
    
    def get_projects(self) -> List[Dict[str, Any]]:
        """获取剪映工程列表"""
        if not self.draft_path:
            return []
        
        projects = []
        draft_path = Path(self.draft_path)
        
        try:
            for project_dir in draft_path.iterdir():
                if project_dir.is_dir():
                    project_info = self._parse_project_info(project_dir)
                    if project_info:
                        projects.append(project_info)
        except Exception as e:
            print(f"获取剪映工程列表失败: {e}")
        
        # 按修改时间排序
        projects.sort(key=lambda x: x.get('modified_time', ''), reverse=True)
        return projects
    
    def _parse_project_info(self, project_dir: Path) -> Optional[Dict[str, Any]]:
        """解析工程信息"""
        try:
            # 查找工程配置文件
            config_files = list(project_dir.glob("*.json"))
            if not config_files:
                return None
            
            config_file = config_files[0]
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 提取工程信息
            project_name = config_data.get('name', project_dir.name)
            
            # 获取修改时间
            modified_time = datetime.fromtimestamp(config_file.stat().st_mtime)
            
            # 获取工程路径
            project_path = str(project_dir)
            
            # 检查素材目录
            materials_dir = project_dir / "materials"
            materials_count = 0
            if materials_dir.exists():
                materials_count = len(list(materials_dir.rglob("*.*")))
            
            return {
                'name': project_name,
                'path': project_path,
                'modified_time': modified_time.isoformat(),
                'materials_count': materials_count,
                'config_file': str(config_file)
            }
            
        except Exception as e:
            print(f"解析工程信息失败 {project_dir}: {e}")
            return None
    
    def export_to_project(self, files: List[str], project_path: str, 
                         create_subfolder: bool = True) -> Tuple[int, int]:
        """
        导出文件到指定工程
        
        Args:
            files: 要导出的文件路径列表
            project_path: 目标工程路径
            create_subfolder: 是否创建子文件夹
            
        Returns:
            Tuple[int, int]: (成功数量, 失败数量)
        """
        if not files:
            return 0, 0
        
        project_dir = Path(project_path)
        if not project_dir.exists():
            print(f"工程目录不存在: {project_path}")
            return 0, len(files)
        
        # 创建素材目录
        materials_dir = project_dir / "materials"
        materials_dir.mkdir(exist_ok=True)
        
        # 如果需要，创建子文件夹
        if create_subfolder:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            target_dir = materials_dir / f"imported_{timestamp}"
            target_dir.mkdir(exist_ok=True)
        else:
            target_dir = materials_dir
        
        success_count = 0
        failed_count = 0
        
        for file_path in files:
            try:
                source_file = Path(file_path)
                if not source_file.exists():
                    failed_count += 1
                    continue
                
                # 生成目标文件路径
                target_file = self._generate_unique_path(target_dir, source_file.name)
                
                # 复制文件
                shutil.copy2(source_file, target_file)
                success_count += 1
                
                print(f"导出成功: {source_file.name} -> {target_file}")
                
            except Exception as e:
                print(f"导出失败 {file_path}: {e}")
                failed_count += 1
        
        return success_count, failed_count
    
    def create_new_project(self, project_name: str) -> Optional[str]:
        """
        创建新的剪映工程
        
        Args:
            project_name: 工程名称
            
        Returns:
            str: 工程路径，失败返回None
        """
        if not self.draft_path:
            return None
        
        # 生成工程ID和目录名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        project_id = f"project_{timestamp}"
        project_dir = Path(self.draft_path) / project_id
        
        try:
            # 创建工程目录
            project_dir.mkdir(exist_ok=True)
            
            # 创建基本目录结构
            (project_dir / "materials").mkdir(exist_ok=True)
            (project_dir / "sequences").mkdir(exist_ok=True)
            
            # 创建工程配置文件
            config_data = {
                "name": project_name,
                "id": project_id,
                "created_time": datetime.now().isoformat(),
                "version": "1.0.0",
                "description": f"由简笔画素材管理软件创建的工程: {project_name}"
            }
            
            config_file = project_dir / f"{project_id}.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            print(f"创建工程成功: {project_name} -> {project_dir}")
            return str(project_dir)
            
        except Exception as e:
            print(f"创建工程失败: {e}")
            return None
    
    def _generate_unique_path(self, directory: Path, filename: str) -> Path:
        """生成唯一的文件路径"""
        target_path = directory / filename
        
        if not target_path.exists():
            return target_path
        
        # 如果文件已存在，添加数字后缀
        name_part = target_path.stem
        ext_part = target_path.suffix
        counter = 1
        
        while target_path.exists():
            new_name = f"{name_part}_{counter}{ext_part}"
            target_path = directory / new_name
            counter += 1
        
        return target_path
    
    def get_project_materials(self, project_path: str) -> List[Dict[str, Any]]:
        """获取工程中的素材列表"""
        materials = []
        project_dir = Path(project_path)
        materials_dir = project_dir / "materials"
        
        if not materials_dir.exists():
            return materials
        
        try:
            for item in materials_dir.rglob("*.*"):
                if item.is_file():
                    stat = item.stat()
                    material_info = {
                        'name': item.name,
                        'path': str(item),
                        'size': stat.st_size,
                        'type': item.suffix.lower().lstrip('.'),
                        'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat()
                    }
                    materials.append(material_info)
        except Exception as e:
            print(f"获取工程素材失败: {e}")
        
        return materials
    
    def validate_project(self, project_path: str) -> bool:
        """验证工程是否有效"""
        project_dir = Path(project_path)
        
        if not project_dir.exists() or not project_dir.is_dir():
            return False
        
        # 检查是否有配置文件
        config_files = list(project_dir.glob("*.json"))
        if not config_files:
            return False
        
        # 检查基本目录结构
        required_dirs = ["materials"]
        for dir_name in required_dirs:
            if not (project_dir / dir_name).exists():
                return False
        
        return True
    
    def get_export_summary(self, files: List[str]) -> Dict[str, Any]:
        """获取导出摘要信息"""
        if not files:
            return {
                'total_files': 0,
                'total_size': 0,
                'file_types': {},
                'size_str': '0 B'
            }
        
        total_size = 0
        file_types = {}
        
        for file_path in files:
            try:
                file_obj = Path(file_path)
                if file_obj.exists():
                    # 计算大小
                    total_size += file_obj.stat().st_size
                    
                    # 统计类型
                    file_type = file_obj.suffix.lower().lstrip('.')
                    if not file_type:
                        file_type = 'unknown'
                    
                    file_types[file_type] = file_types.get(file_type, 0) + 1
            except Exception:
                continue
        
        # 格式化大小
        size_str = self._format_file_size(total_size)
        
        return {
            'total_files': len(files),
            'total_size': total_size,
            'file_types': file_types,
            'size_str': size_str
        }
    
    def _format_file_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
