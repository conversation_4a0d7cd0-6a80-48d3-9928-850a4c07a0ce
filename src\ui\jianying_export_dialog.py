# -*- coding: utf-8 -*-
"""
剪映导出对话框 - 导出素材到剪映工程
"""

import os
from pathlib import Path
from typing import List, Dict, Any

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QListWidget, QListWidgetItem,
                            QTextEdit, QGroupBox, QCheckBox, QLineEdit,
                            QMessageBox, QProgressBar, QFormLayout,
                            QSplitter, QFrame)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QObject
from PyQt6.QtGui import QFont, QIcon

from utils.jianying_export import JianyingExporter
from utils.config_manager import ConfigManager


class ExportWorker(QObject):
    """导出工作线程"""
    
    # 信号
    progress_updated = pyqtSignal(int)  # 进度更新
    file_exported = pyqtSignal(str)    # 文件导出完成
    export_finished = pyqtSignal(int, int)  # 导出完成 (成功数, 失败数)
    
    def __init__(self, exporter: JianyingExporter, files: List[str], 
                 project_path: str, create_subfolder: bool):
        super().__init__()
        self.exporter = exporter
        self.files = files
        self.project_path = project_path
        self.create_subfolder = create_subfolder
        self.is_running = False
    
    def start_export(self):
        """开始导出"""
        self.is_running = True
        success_count, failed_count = self.exporter.export_to_project(
            self.files, self.project_path, self.create_subfolder
        )
        self.export_finished.emit(success_count, failed_count)
    
    def stop_export(self):
        """停止导出"""
        self.is_running = False


class JianyingExportDialog(QDialog):
    """剪映导出对话框"""
    
    # 信号
    export_completed = pyqtSignal(int, int)  # 导出完成信号
    
    def __init__(self, files: List[str], config_manager: ConfigManager, parent=None):
        super().__init__(parent)
        self.files = files
        self.config_manager = config_manager
        self.exporter = JianyingExporter(config_manager)
        
        self.projects = []
        self.selected_project = None
        self.export_worker = None
        self.export_thread = None
        
        self.init_ui()
        self.load_projects()
        self.update_export_info()
        
        # 检查剪映是否可用
        if not self.exporter.is_jianying_available():
            self.show_jianying_not_found()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("导出到剪映")
        self.setModal(True)
        self.resize(800, 600)
        
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：工程列表
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 工程列表标题
        projects_label = QLabel("剪映工程列表")
        projects_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        left_layout.addWidget(projects_label)
        
        # 工程列表
        self.projects_list = QListWidget()
        self.projects_list.itemClicked.connect(self.on_project_selected)
        left_layout.addWidget(self.projects_list)
        
        # 工程操作按钮
        project_buttons_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_projects)
        project_buttons_layout.addWidget(self.refresh_btn)
        
        self.new_project_btn = QPushButton("新建工程")
        self.new_project_btn.clicked.connect(self.create_new_project)
        project_buttons_layout.addWidget(self.new_project_btn)
        
        project_buttons_layout.addStretch()
        
        left_layout.addLayout(project_buttons_layout)
        
        splitter.addWidget(left_widget)
        
        # 右侧：导出信息和设置
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 导出信息
        info_group = QGroupBox("导出信息")
        info_layout = QVBoxLayout(info_group)
        
        self.export_info_text = QTextEdit()
        self.export_info_text.setMaximumHeight(150)
        self.export_info_text.setReadOnly(True)
        info_layout.addWidget(self.export_info_text)
        
        right_layout.addWidget(info_group)
        
        # 导出设置
        settings_group = QGroupBox("导出设置")
        settings_layout = QFormLayout(settings_group)
        
        self.create_subfolder_check = QCheckBox("创建子文件夹")
        self.create_subfolder_check.setChecked(True)
        self.create_subfolder_check.setToolTip("在工程的materials目录下创建带时间戳的子文件夹")
        settings_layout.addRow("", self.create_subfolder_check)
        
        right_layout.addWidget(settings_group)
        
        # 工程详情
        details_group = QGroupBox("工程详情")
        details_layout = QVBoxLayout(details_group)
        
        self.project_details_text = QTextEdit()
        self.project_details_text.setMaximumHeight(200)
        self.project_details_text.setReadOnly(True)
        details_layout.addWidget(self.project_details_text)
        
        right_layout.addWidget(details_group)
        
        right_layout.addStretch()
        
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([400, 400])
        layout.addWidget(splitter)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        self.export_btn = QPushButton("导出")
        self.export_btn.clicked.connect(self.start_export)
        self.export_btn.setDefault(True)
        self.export_btn.setEnabled(False)
        button_layout.addWidget(self.export_btn)
        
        layout.addLayout(button_layout)
        
        # 应用样式
        self.apply_styles()
    
    def apply_styles(self):
        """应用样式"""
        style = """
        QDialog {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QLabel {
            color: #ffffff;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 8px 15px;
            color: #ffffff;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QPushButton:disabled {
            background-color: #3a3a3a;
            color: #666666;
        }
        
        QListWidget {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 5px;
        }
        
        QListWidget::item {
            padding: 10px;
            border-bottom: 1px solid #4a4a4a;
        }
        
        QListWidget::item:selected {
            background-color: #5a5a5a;
        }
        
        QListWidget::item:hover {
            background-color: #4a4a4a;
        }
        
        QTextEdit {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            color: #ffffff;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #5a5a5a;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        """
        self.setStyleSheet(style)
    
    def show_jianying_not_found(self):
        """显示剪映未找到的提示"""
        QMessageBox.warning(
            self, "剪映未找到",
            "未能检测到剪映安装路径。\n\n"
            "请确保已安装剪映专业版，或手动设置剪映路径。"
        )
    
    def load_projects(self):
        """加载剪映工程列表"""
        self.projects_list.clear()
        self.projects = self.exporter.get_projects()
        
        if not self.projects:
            item = QListWidgetItem("未找到剪映工程")
            item.setFlags(Qt.ItemFlag.NoItemFlags)
            self.projects_list.addItem(item)
            return
        
        for project in self.projects:
            item = QListWidgetItem()
            
            # 设置显示文本
            name = project.get('name', '未知工程')
            modified_time = project.get('modified_time', '')
            materials_count = project.get('materials_count', 0)
            
            if modified_time:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(modified_time)
                    time_str = dt.strftime("%Y-%m-%d %H:%M")
                except:
                    time_str = modified_time[:16]
            else:
                time_str = "未知时间"
            
            display_text = f"{name}\n修改时间: {time_str} | 素材数: {materials_count}"
            item.setText(display_text)
            item.setData(Qt.ItemDataRole.UserRole, project)
            
            self.projects_list.addItem(item)
    
    def on_project_selected(self, item: QListWidgetItem):
        """工程选择事件"""
        project_data = item.data(Qt.ItemDataRole.UserRole)
        if project_data:
            self.selected_project = project_data
            self.update_project_details()
            self.export_btn.setEnabled(True)
    
    def update_project_details(self):
        """更新工程详情"""
        if not self.selected_project:
            self.project_details_text.clear()
            return
        
        details = []
        details.append(f"工程名称: {self.selected_project.get('name', '未知')}")
        details.append(f"工程路径: {self.selected_project.get('path', '未知')}")
        details.append(f"修改时间: {self.selected_project.get('modified_time', '未知')}")
        details.append(f"现有素材数: {self.selected_project.get('materials_count', 0)}")
        
        # 获取工程中的素材列表
        project_path = self.selected_project.get('path', '')
        if project_path:
            materials = self.exporter.get_project_materials(project_path)
            if materials:
                details.append(f"\n现有素材:")
                for material in materials[:10]:  # 只显示前10个
                    details.append(f"  - {material['name']}")
                if len(materials) > 10:
                    details.append(f"  ... 还有 {len(materials) - 10} 个文件")
        
        self.project_details_text.setPlainText('\n'.join(details))
    
    def update_export_info(self):
        """更新导出信息"""
        summary = self.exporter.get_export_summary(self.files)
        
        info_lines = []
        info_lines.append(f"待导出文件数: {summary['total_files']}")
        info_lines.append(f"总大小: {summary['size_str']}")
        
        if summary['file_types']:
            info_lines.append("\n文件类型分布:")
            for file_type, count in summary['file_types'].items():
                info_lines.append(f"  {file_type.upper()}: {count} 个")
        
        info_lines.append(f"\n文件列表:")
        for i, file_path in enumerate(self.files[:10]):  # 只显示前10个
            file_name = Path(file_path).name
            info_lines.append(f"  {i+1}. {file_name}")
        
        if len(self.files) > 10:
            info_lines.append(f"  ... 还有 {len(self.files) - 10} 个文件")
        
        self.export_info_text.setPlainText('\n'.join(info_lines))
    
    def create_new_project(self):
        """创建新工程"""
        from PyQt6.QtWidgets import QInputDialog
        
        project_name, ok = QInputDialog.getText(
            self, "新建工程", "请输入工程名称:", text="新建工程"
        )
        
        if ok and project_name.strip():
            project_path = self.exporter.create_new_project(project_name.strip())
            if project_path:
                QMessageBox.information(self, "成功", f"工程创建成功: {project_name}")
                self.load_projects()
                
                # 自动选择新创建的工程
                for i in range(self.projects_list.count()):
                    item = self.projects_list.item(i)
                    project_data = item.data(Qt.ItemDataRole.UserRole)
                    if project_data and project_data.get('path') == project_path:
                        self.projects_list.setCurrentItem(item)
                        self.on_project_selected(item)
                        break
            else:
                QMessageBox.warning(self, "失败", "工程创建失败")
    
    def start_export(self):
        """开始导出"""
        if not self.selected_project:
            QMessageBox.warning(self, "错误", "请选择一个工程")
            return
        
        if not self.files:
            QMessageBox.warning(self, "错误", "没有要导出的文件")
            return
        
        # 禁用按钮
        self.export_btn.setEnabled(False)
        self.cancel_btn.setText("停止")
        
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 创建工作线程
        self.export_thread = QThread()
        self.export_worker = ExportWorker(
            self.exporter,
            self.files,
            self.selected_project['path'],
            self.create_subfolder_check.isChecked()
        )
        self.export_worker.moveToThread(self.export_thread)
        
        # 连接信号
        self.export_thread.started.connect(self.export_worker.start_export)
        self.export_worker.export_finished.connect(self.on_export_finished)
        
        # 启动线程
        self.export_thread.start()
    
    def on_export_finished(self, success_count: int, failed_count: int):
        """导出完成处理"""
        # 清理线程
        if self.export_thread:
            self.export_thread.quit()
            self.export_thread.wait()
            self.export_thread = None
            self.export_worker = None
        
        # 隐藏进度条
        self.progress_bar.setVisible(False)
        
        # 恢复按钮
        self.export_btn.setEnabled(True)
        self.cancel_btn.setText("取消")
        
        # 显示结果
        total_files = success_count + failed_count
        if success_count == total_files:
            QMessageBox.information(
                self, "导出完成", 
                f"成功导出 {success_count} 个文件到剪映工程"
            )
        else:
            QMessageBox.warning(
                self, "导出完成", 
                f"导出完成！\n成功: {success_count} 个\n失败: {failed_count} 个"
            )
        
        # 发送完成信号
        self.export_completed.emit(success_count, failed_count)
        
        if success_count > 0:
            self.accept()
    
    def reject(self):
        """取消或停止"""
        if self.export_worker and self.export_thread:
            # 停止导出
            self.export_worker.stop_export()
            if self.export_thread:
                self.export_thread.quit()
                self.export_thread.wait()
        
        super().reject()
