# -*- coding: utf-8 -*-
"""
回收站管理 - 回收站文件管理界面
"""

import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                            QTableWidgetItem, QPushButton, QLabel, QMessageBox,
                            QProgressBar, QCheckBox, QSpinBox, QFormLayout,
                            QGroupBox, QHeaderView, QAbstractItemView)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QThread, QObject
from PyQt6.QtGui import QFont, QIcon

from models.database import DatabaseManager
from utils.config_manager import ConfigManager
from utils.file_operations import FileOperations


class CleanupWorker(QObject):
    """清理工作线程"""
    
    # 信号
    progress_updated = pyqtSignal(int)  # 进度更新
    file_cleaned = pyqtSignal(str)     # 文件清理完成
    cleanup_finished = pyqtSignal(int) # 清理完成，参数为清理的文件数量
    
    def __init__(self, db_manager: DatabaseManager, days: int):
        super().__init__()
        self.db_manager = db_manager
        self.days = days
        self.is_running = False
    
    def start_cleanup(self):
        """开始清理"""
        self.is_running = True
        
        # 获取需要清理的文件
        deleted_files = self.db_manager.get_deleted_files()
        cutoff_date = datetime.now() - timedelta(days=self.days)
        
        files_to_clean = []
        for file_info in deleted_files:
            delete_time_str = file_info.get('delete_time', '')
            if delete_time_str:
                try:
                    delete_time = datetime.fromisoformat(delete_time_str)
                    if delete_time < cutoff_date:
                        files_to_clean.append(file_info)
                except ValueError:
                    continue
        
        total_files = len(files_to_clean)
        cleaned_count = 0
        
        for i, file_info in enumerate(files_to_clean):
            if not self.is_running:
                break
            
            try:
                # 删除物理文件
                file_path = file_info.get('path', '')
                if file_path and os.path.exists(file_path):
                    os.remove(file_path)
                
                # 从数据库删除记录
                self.db_manager.permanently_delete_file(file_info['id'])
                
                cleaned_count += 1
                self.file_cleaned.emit(file_info.get('name', ''))
                
            except Exception as e:
                print(f"清理文件失败 {file_info.get('name', '')}: {e}")
            
            # 更新进度
            progress = int((i + 1) * 100 / total_files) if total_files > 0 else 100
            self.progress_updated.emit(progress)
        
        self.cleanup_finished.emit(cleaned_count)
    
    def stop_cleanup(self):
        """停止清理"""
        self.is_running = False


class RecycleBin(QWidget):
    """回收站管理界面"""
    
    # 信号
    files_restored = pyqtSignal(list)  # 文件还原完成
    files_deleted = pyqtSignal(list)   # 文件永久删除完成
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager):
        super().__init__()
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.file_operations = FileOperations(config_manager, db_manager)
        
        self.deleted_files = []
        self.cleanup_worker = None
        self.cleanup_thread = None
        
        self.init_ui()
        self.load_deleted_files()
        self.setup_auto_cleanup()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("回收站管理")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 统计信息
        self.stats_label = QLabel()
        self.stats_label.setFont(QFont("Microsoft YaHei", 10))
        layout.addWidget(self.stats_label)
        
        # 操作按钮区域
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_deleted_files)
        button_layout.addWidget(self.refresh_btn)
        
        self.restore_btn = QPushButton("还原选中")
        self.restore_btn.clicked.connect(self.restore_selected)
        self.restore_btn.setEnabled(False)
        button_layout.addWidget(self.restore_btn)
        
        self.delete_btn = QPushButton("永久删除选中")
        self.delete_btn.clicked.connect(self.permanently_delete_selected)
        self.delete_btn.setEnabled(False)
        button_layout.addWidget(self.delete_btn)
        
        button_layout.addStretch()
        
        self.empty_btn = QPushButton("清空回收站")
        self.empty_btn.clicked.connect(self.empty_recycle_bin)
        button_layout.addWidget(self.empty_btn)
        
        layout.addLayout(button_layout)
        
        # 文件列表
        self.file_table = QTableWidget()
        self.file_table.setColumnCount(5)
        self.file_table.setHorizontalHeaderLabels(["文件名", "原始位置", "删除时间", "大小", "类型"])
        
        # 设置表格属性
        self.file_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.file_table.setAlternatingRowColors(True)
        self.file_table.setSortingEnabled(True)
        
        # 调整列宽
        header = self.file_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        
        # 连接选择变化信号
        self.file_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        layout.addWidget(self.file_table)
        
        # 自动清理设置
        cleanup_group = QGroupBox("自动清理设置")
        cleanup_layout = QFormLayout(cleanup_group)
        
        self.auto_cleanup_check = QCheckBox("启用自动清理")
        self.auto_cleanup_check.setChecked(True)
        cleanup_layout.addRow("", self.auto_cleanup_check)
        
        self.cleanup_days_spin = QSpinBox()
        self.cleanup_days_spin.setMinimum(1)
        self.cleanup_days_spin.setMaximum(365)
        self.cleanup_days_spin.setValue(30)
        self.cleanup_days_spin.setSuffix(" 天")
        cleanup_layout.addRow("保留天数:", self.cleanup_days_spin)
        
        self.cleanup_now_btn = QPushButton("立即清理")
        self.cleanup_now_btn.clicked.connect(self.start_manual_cleanup)
        cleanup_layout.addRow("", self.cleanup_now_btn)
        
        # 清理进度条
        self.cleanup_progress = QProgressBar()
        self.cleanup_progress.setVisible(False)
        cleanup_layout.addRow("清理进度:", self.cleanup_progress)
        
        layout.addWidget(cleanup_group)
        
        # 应用样式
        self.apply_styles()
    
    def apply_styles(self):
        """应用样式"""
        style = """
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QLabel {
            color: #ffffff;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 8px 15px;
            color: #ffffff;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QPushButton:disabled {
            background-color: #3a3a3a;
            color: #666666;
        }
        
        QTableWidget {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 5px;
            gridline-color: #5a5a5a;
        }
        
        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #4a4a4a;
        }
        
        QTableWidget::item:selected {
            background-color: #5a5a5a;
        }
        
        QHeaderView::section {
            background-color: #4a4a4a;
            color: #ffffff;
            padding: 8px;
            border: 1px solid #5a5a5a;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #5a5a5a;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        """
        self.setStyleSheet(style)
    
    def load_deleted_files(self):
        """加载已删除的文件"""
        self.deleted_files = self.db_manager.get_deleted_files()
        self.update_file_table()
        self.update_stats()
    
    def update_file_table(self):
        """更新文件表格"""
        self.file_table.setRowCount(len(self.deleted_files))
        
        for row, file_info in enumerate(self.deleted_files):
            # 文件名
            name_item = QTableWidgetItem(file_info.get('name', ''))
            self.file_table.setItem(row, 0, name_item)
            
            # 原始位置
            original_path = file_info.get('original_path', '')
            if original_path:
                original_item = QTableWidgetItem(str(Path(original_path).parent))
            else:
                original_item = QTableWidgetItem("未知")
            self.file_table.setItem(row, 1, original_item)
            
            # 删除时间
            delete_time = file_info.get('delete_time', '')
            if delete_time:
                try:
                    dt = datetime.fromisoformat(delete_time)
                    time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
                except ValueError:
                    time_str = delete_time
            else:
                time_str = "未知"
            time_item = QTableWidgetItem(time_str)
            self.file_table.setItem(row, 2, time_item)
            
            # 文件大小
            file_path = file_info.get('path', '')
            if file_path and os.path.exists(file_path):
                size = os.path.getsize(file_path)
                size_str = self.format_file_size(size)
            else:
                size_str = "未知"
            size_item = QTableWidgetItem(size_str)
            self.file_table.setItem(row, 3, size_item)
            
            # 文件类型
            file_type = file_info.get('type', '').upper()
            type_item = QTableWidgetItem(file_type)
            self.file_table.setItem(row, 4, type_item)
    
    def update_stats(self):
        """更新统计信息"""
        total_files = len(self.deleted_files)
        total_size = 0
        
        for file_info in self.deleted_files:
            file_path = file_info.get('path', '')
            if file_path and os.path.exists(file_path):
                total_size += os.path.getsize(file_path)
        
        size_str = self.format_file_size(total_size)
        self.stats_label.setText(f"回收站中共有 {total_files} 个文件，占用空间 {size_str}")
    
    def format_file_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    
    def on_selection_changed(self):
        """选择变化处理"""
        selected_rows = set()
        for item in self.file_table.selectedItems():
            selected_rows.add(item.row())
        
        has_selection = len(selected_rows) > 0
        self.restore_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
    
    def get_selected_files(self) -> List[Dict[str, Any]]:
        """获取选中的文件"""
        selected_rows = set()
        for item in self.file_table.selectedItems():
            selected_rows.add(item.row())
        
        return [self.deleted_files[row] for row in selected_rows]
    
    def restore_selected(self):
        """还原选中的文件"""
        selected_files = self.get_selected_files()
        if not selected_files:
            return
        
        reply = QMessageBox.question(
            self, "确认还原",
            f"确定要还原选中的 {len(selected_files)} 个文件吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            success_count = 0
            for file_info in selected_files:
                if self.file_operations.restore_file(file_info['id']):
                    success_count += 1
            
            QMessageBox.information(self, "还原完成", f"成功还原 {success_count} 个文件")
            self.load_deleted_files()
            self.files_restored.emit(selected_files)
    
    def permanently_delete_selected(self):
        """永久删除选中的文件"""
        selected_files = self.get_selected_files()
        if not selected_files:
            return
        
        reply = QMessageBox.warning(
            self, "确认永久删除",
            f"确定要永久删除选中的 {len(selected_files)} 个文件吗？\n此操作无法撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            success_count = 0
            for file_info in selected_files:
                try:
                    # 删除物理文件
                    file_path = file_info.get('path', '')
                    if file_path and os.path.exists(file_path):
                        os.remove(file_path)
                    
                    # 从数据库删除
                    if self.db_manager.permanently_delete_file(file_info['id']):
                        success_count += 1
                except Exception as e:
                    print(f"删除文件失败 {file_info.get('name', '')}: {e}")
            
            QMessageBox.information(self, "删除完成", f"成功删除 {success_count} 个文件")
            self.load_deleted_files()
            self.files_deleted.emit(selected_files)
    
    def empty_recycle_bin(self):
        """清空回收站"""
        if not self.deleted_files:
            QMessageBox.information(self, "提示", "回收站已经是空的")
            return
        
        reply = QMessageBox.warning(
            self, "确认清空回收站",
            f"确定要清空回收站吗？这将永久删除所有 {len(self.deleted_files)} 个文件！\n此操作无法撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.start_cleanup_all()
    
    def start_manual_cleanup(self):
        """开始手动清理"""
        days = self.cleanup_days_spin.value()
        self.start_cleanup(days)
    
    def start_cleanup_all(self):
        """清理所有文件"""
        self.start_cleanup(0)  # 0天表示清理所有文件
    
    def start_cleanup(self, days: int):
        """开始清理操作"""
        if self.cleanup_worker and self.cleanup_thread:
            return  # 已经在清理中
        
        self.cleanup_progress.setVisible(True)
        self.cleanup_progress.setValue(0)
        self.cleanup_now_btn.setEnabled(False)
        
        # 创建工作线程
        self.cleanup_thread = QThread()
        self.cleanup_worker = CleanupWorker(self.db_manager, days)
        self.cleanup_worker.moveToThread(self.cleanup_thread)
        
        # 连接信号
        self.cleanup_thread.started.connect(self.cleanup_worker.start_cleanup)
        self.cleanup_worker.progress_updated.connect(self.cleanup_progress.setValue)
        self.cleanup_worker.cleanup_finished.connect(self.on_cleanup_finished)
        
        # 启动线程
        self.cleanup_thread.start()
    
    def on_cleanup_finished(self, cleaned_count: int):
        """清理完成处理"""
        self.cleanup_progress.setVisible(False)
        self.cleanup_now_btn.setEnabled(True)
        
        # 清理线程
        if self.cleanup_thread:
            self.cleanup_thread.quit()
            self.cleanup_thread.wait()
            self.cleanup_thread = None
            self.cleanup_worker = None
        
        # 刷新列表
        self.load_deleted_files()
        
        QMessageBox.information(self, "清理完成", f"成功清理 {cleaned_count} 个文件")
    
    def setup_auto_cleanup(self):
        """设置自动清理"""
        # 创建定时器，每天检查一次
        self.auto_cleanup_timer = QTimer()
        self.auto_cleanup_timer.timeout.connect(self.check_auto_cleanup)
        self.auto_cleanup_timer.start(24 * 60 * 60 * 1000)  # 24小时
        
        # 启动时也检查一次
        QTimer.singleShot(5000, self.check_auto_cleanup)  # 5秒后检查
    
    def check_auto_cleanup(self):
        """检查是否需要自动清理"""
        if not self.auto_cleanup_check.isChecked():
            return
        
        days = self.cleanup_days_spin.value()
        
        # 检查是否有需要清理的文件
        deleted_files = self.db_manager.get_deleted_files()
        cutoff_date = datetime.now() - timedelta(days=days)
        
        files_to_clean = 0
        for file_info in deleted_files:
            delete_time_str = file_info.get('delete_time', '')
            if delete_time_str:
                try:
                    delete_time = datetime.fromisoformat(delete_time_str)
                    if delete_time < cutoff_date:
                        files_to_clean += 1
                except ValueError:
                    continue
        
        if files_to_clean > 0:
            print(f"自动清理: 发现 {files_to_clean} 个过期文件")
            self.start_cleanup(days)
